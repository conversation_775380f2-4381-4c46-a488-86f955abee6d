package common

import (
	"regexp"
	"strings"
)

// SanitizeErrorMessage 对错误信息进行脱敏处理
func SanitizeErrorMessage(errorMsg string) string {
	if errorMsg == "" {
		return errorMsg
	}

	// 脱敏API密钥
	apiKeyRegex := regexp.MustCompile(`(sk-[a-zA-Z0-9]{20,})`)
	errorMsg = apiKeyRegex.ReplaceAllString(errorMsg, "sk-***")

	// 脱敏Bearer token
	bearerRegex := regexp.MustCompile(`(Bearer\s+[a-zA-Z0-9\-_\.]{20,})`)
	errorMsg = bearerRegex.ReplaceAllString(errorMsg, "Bearer ***")

	// 脱敏邮箱地址
	emailRegex := regexp.MustCompile(`([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})`)
	errorMsg = emailRegex.ReplaceAllString(errorMsg, "***@***.***")

	// 脱敏IP地址
	ipRegex := regexp.MustCompile(`(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})`)
	errorMsg = ipRegex.ReplaceAllString(errorMsg, "***.***.***.***")

	// 脱敏URL中的敏感信息
	urlRegex := regexp.MustCompile(`(https?://[^\s]+)`)
	errorMsg = urlRegex.ReplaceAllStringFunc(errorMsg, func(url string) string {
		// 保留域名，脱敏路径参数
		if strings.Contains(url, "?") {
			parts := strings.Split(url, "?")
			return parts[0] + "?***"
		}
		return url
	})

	return errorMsg
}
