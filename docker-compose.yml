services:
  cees-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: cees-api
    restart: always
    command: --log-dir /app/logs
    ports:
      - "3000:3000"
    volumes:
      - ./data:/data
      - ./logs:/app/logs
    environment:
      - SQL_DSN=root:123456@tcp(mysql:3306)/new-api?parseTime=true
      - REDIS_CONN_STRING=redis://redis:6379/0
      - TZ=Asia/Shanghai
      - ERROR_LOG_ENABLED=true # 是否启用错误日志记录
      - SESSION_SECRET=random_string_for_cees_api_system
      - PORT=3000
      # 速率限制配置 - 放宽限制以避免429错误
      - GLOBAL_WEB_RATE_LIMIT_ENABLE=true
      - GLOBAL_WEB_RATE_LIMIT=300  # 增加到300次/180秒
      - GLOBAL_WEB_RATE_LIMIT_DURATION=180
      - GLOBAL_API_RATE_LIMIT_ENABLE=true
      - GLOBAL_API_RATE_LIMIT=600  # 增加到600次/180秒
      - GLOBAL_API_RATE_LIMIT_DURATION=180

    depends_on:
      - redis
      - mysql
    healthcheck:
      test: ["CMD-SHELL", "wget -q -O - http://localhost:3000/api/status | grep -o '\"success\":\\s*true' | awk -F: '{print $$2}'"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:latest
    container_name: redis
    restart: always

  mysql:
    image: mysql:8.2
    container_name: mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456  # Ensure this matches the password in SQL_DSN
      MYSQL_DATABASE: new-api
    volumes:
      - mysql_data:/var/lib/mysql
    # ports:
    #   - "3306:3306"  # If you want to access MySQL from outside Docker, uncomment

volumes:
  mysql_data:
