package relay

import (
	"cees-api/constant"
	commonconstant "cees-api/constant"
	"cees-api/relay/channel"
	"cees-api/relay/channel/ali"
	"cees-api/relay/channel/aws"
	"cees-api/relay/channel/baidu"
	"cees-api/relay/channel/baidu_v2"
	"cees-api/relay/channel/claude"
	"cees-api/relay/channel/cloudflare"
	"cees-api/relay/channel/cohere"
	"cees-api/relay/channel/coze"
	"cees-api/relay/channel/deepseek"
	"cees-api/relay/channel/dify"
	"cees-api/relay/channel/gemini"
	"cees-api/relay/channel/jimeng"
	"cees-api/relay/channel/jina"
	"cees-api/relay/channel/mistral"
	"cees-api/relay/channel/mokaai"
	"cees-api/relay/channel/ollama"
	"cees-api/relay/channel/openai"
	"cees-api/relay/channel/palm"
	"cees-api/relay/channel/perplexity"
	"cees-api/relay/channel/siliconflow"
	taskjimeng "cees-api/relay/channel/task/jimeng"
	"cees-api/relay/channel/task/kling"
	"cees-api/relay/channel/task/suno"
	"cees-api/relay/channel/tencent"
	"cees-api/relay/channel/vertex"
	"cees-api/relay/channel/volcengine"
	"cees-api/relay/channel/xai"
	"cees-api/relay/channel/xunfei"
	"cees-api/relay/channel/zhipu"
	"cees-api/relay/channel/zhipu_4v"
)

func GetAdaptor(apiType int) channel.Adaptor {
	switch apiType {
	case constant.APITypeAli:
		return &ali.Adaptor{}
	case constant.APITypeAnthropic:
		return &claude.Adaptor{}
	case constant.APITypeBaidu:
		return &baidu.Adaptor{}
	case constant.APITypeGemini:
		return &gemini.Adaptor{}
	case constant.APITypeOpenAI:
		return &openai.Adaptor{}
	case constant.APITypePaLM:
		return &palm.Adaptor{}
	case constant.APITypeTencent:
		return &tencent.Adaptor{}
	case constant.APITypeXunfei:
		return &xunfei.Adaptor{}
	case constant.APITypeZhipu:
		return &zhipu.Adaptor{}
	case constant.APITypeZhipuV4:
		return &zhipu_4v.Adaptor{}
	case constant.APITypeOllama:
		return &ollama.Adaptor{}
	case constant.APITypePerplexity:
		return &perplexity.Adaptor{}
	case constant.APITypeAws:
		return &aws.Adaptor{}
	case constant.APITypeCohere:
		return &cohere.Adaptor{}
	case constant.APITypeDify:
		return &dify.Adaptor{}
	case constant.APITypeJina:
		return &jina.Adaptor{}
	case constant.APITypeCloudflare:
		return &cloudflare.Adaptor{}
	case constant.APITypeSiliconFlow:
		return &siliconflow.Adaptor{}
	case constant.APITypeVertexAi:
		return &vertex.Adaptor{}
	case constant.APITypeMistral:
		return &mistral.Adaptor{}
	case constant.APITypeDeepSeek:
		return &deepseek.Adaptor{}
	case constant.APITypeMokaAI:
		return &mokaai.Adaptor{}
	case constant.APITypeVolcEngine:
		return &volcengine.Adaptor{}
	case constant.APITypeBaiduV2:
		return &baidu_v2.Adaptor{}
	case constant.APITypeOpenRouter:
		return &openai.Adaptor{}
	case constant.APITypeXinference:
		return &openai.Adaptor{}
	case constant.APITypeXai:
		return &xai.Adaptor{}
	case constant.APITypeCoze:
		return &coze.Adaptor{}
	case constant.APITypeJimeng:
		return &jimeng.Adaptor{}
	}
	return nil
}

func GetTaskAdaptor(platform commonconstant.TaskPlatform) channel.TaskAdaptor {
	switch platform {
	//case constant.APITypeAIProxyLibrary:
	//	return &aiproxy.Adaptor{}
	case commonconstant.TaskPlatformSuno:
		return &suno.TaskAdaptor{}
	case commonconstant.TaskPlatformKling:
		return &kling.TaskAdaptor{}
	case commonconstant.TaskPlatformJimeng:
		return &taskjimeng.TaskAdaptor{}
	}
	return nil
}
