package middleware

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
	"cees-api/common"
	"runtime/debug"
)

func RelayPanicRecover() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				// 记录原始错误到系统日志（内部使用）
				common.SysError(fmt.Sprintf("panic detected: %v", err))
				common.SysError(fmt.Sprintf("stacktrace from panic: %s", string(debug.Stack())))

				// 对用户可见的错误信息进行脱敏处理
				errorMsg := fmt.Sprintf("Panic detected, error: %v. Please contact system administrator", err)
				sanitizedErrorMsg := common.SanitizeErrorMessage(errorMsg)

				c.<PERSON>(http.StatusInternalServerError, gin.H{
					"error": gin.H{
						"message": sanitizedErrorMsg,
						"type":    "cees_api_panic",
					},
				})
				c.Abort()
			}
		}()
		c.Next()
	}
}
