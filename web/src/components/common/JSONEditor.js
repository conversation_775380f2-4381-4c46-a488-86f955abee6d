import React, { useState, useEffect, useRef } from 'react';
import { Input, Button, Typography, Space, Toast } from '@douyinfe/semi-ui';
import { IconCode, IconEyeOpened, IconEyeClosed } from '@douyinfe/semi-icons';

const { TextArea } = Input;
const { Text } = Typography;

const JSONEditor = ({ 
  value = '', 
  onChange, 
  placeholder = '请输入JSON格式的数据',
  height = 200,
  disabled = false,
  showFormatButton = true,
  showValidation = true,
  ...props 
}) => {
  const [jsonValue, setJsonValue] = useState(value);
  const [isValid, setIsValid] = useState(true);
  const [errorMessage, setErrorMessage] = useState('');
  const [isFormatted, setIsFormatted] = useState(false);
  const textAreaRef = useRef(null);

  useEffect(() => {
    setJsonValue(value);
    validateJSON(value);
  }, [value]);

  const validateJSON = (jsonString) => {
    if (!jsonString.trim()) {
      setIsValid(true);
      setErrorMessage('');
      return true;
    }

    try {
      JSON.parse(jsonString);
      setIsValid(true);
      setErrorMessage('');
      return true;
    } catch (error) {
      setIsValid(false);
      setErrorMessage(error.message);
      return false;
    }
  };

  const handleChange = (newValue) => {
    setJsonValue(newValue);
    const valid = validateJSON(newValue);
    
    if (onChange) {
      onChange(newValue, valid);
    }
  };

  const formatJSON = () => {
    if (!jsonValue.trim()) {
      Toast.warning('请先输入JSON内容');
      return;
    }

    try {
      const parsed = JSON.parse(jsonValue);
      const formatted = JSON.stringify(parsed, null, 2);
      setJsonValue(formatted);
      setIsFormatted(true);
      setIsValid(true);
      setErrorMessage('');
      
      if (onChange) {
        onChange(formatted, true);
      }
      
      Toast.success('JSON格式化成功');
    } catch (error) {
      Toast.error('JSON格式错误，无法格式化');
    }
  };

  const compactJSON = () => {
    if (!jsonValue.trim()) {
      Toast.warning('请先输入JSON内容');
      return;
    }

    try {
      const parsed = JSON.parse(jsonValue);
      const compacted = JSON.stringify(parsed);
      setJsonValue(compacted);
      setIsFormatted(false);
      setIsValid(true);
      setErrorMessage('');
      
      if (onChange) {
        onChange(compacted, true);
      }
      
      Toast.success('JSON压缩成功');
    } catch (error) {
      Toast.error('JSON格式错误，无法压缩');
    }
  };

  const clearContent = () => {
    setJsonValue('');
    setIsValid(true);
    setErrorMessage('');
    setIsFormatted(false);
    
    if (onChange) {
      onChange('', true);
    }
  };

  return (
    <div style={{ width: '100%' }}>
      {showFormatButton && (
        <div style={{ marginBottom: 8 }}>
          <Space>
            <Button 
              icon={<IconCode />} 
              size="small" 
              onClick={formatJSON}
              disabled={disabled || !jsonValue.trim()}
            >
              格式化
            </Button>
            <Button 
              icon={isFormatted ? <IconEyeClosed /> : <IconEyeOpened />} 
              size="small" 
              onClick={compactJSON}
              disabled={disabled || !jsonValue.trim()}
            >
              压缩
            </Button>
            <Button 
              size="small" 
              type="tertiary"
              onClick={clearContent}
              disabled={disabled || !jsonValue.trim()}
            >
              清空
            </Button>
          </Space>
        </div>
      )}
      
      <TextArea
        ref={textAreaRef}
        value={jsonValue}
        onChange={handleChange}
        placeholder={placeholder}
        style={{ 
          height: height,
          fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
          fontSize: '13px',
          lineHeight: '1.4'
        }}
        disabled={disabled}
        {...props}
      />
      
      {showValidation && errorMessage && (
        <div style={{ marginTop: 4 }}>
          <Text type="danger" size="small">
            JSON格式错误: {errorMessage}
          </Text>
        </div>
      )}
      
      {showValidation && isValid && jsonValue.trim() && (
        <div style={{ marginTop: 4 }}>
          <Text type="success" size="small">
            JSON格式正确
          </Text>
        </div>
      )}
    </div>
  );
};

export default JSONEditor;
