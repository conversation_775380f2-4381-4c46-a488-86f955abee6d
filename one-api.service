# File path: /etc/systemd/system/cees-api.service
# sudo systemctl daemon-reload
# sudo systemctl start cees-api
# sudo systemctl enable cees-api
# sudo systemctl status cees-api
[Unit]
Description=CEES API Service
After=network.target

[Service]
User=ubuntu  # 注意修改用户名
WorkingDirectory=/path/to/cees-api  # 注意修改路径
ExecStart=/path/to/cees-api/cees-api --port 3000 --log-dir /path/to/cees-api/logs  # 注意修改路径和端口号
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
